import os
import asyncio
import json
import tempfile
from urllib.parse import urlparse
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import urllib.request
import whisper

# Carrega o modelo Whisper
whisper_model = whisper.load_model("base")

class AudioTranscriptionHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        try:
            # Parse da URL para obter query parameters
            parsed_url = urlparse(self.path)
            query_params = parse_qs(parsed_url.query)
            
            # Verifica se audio_url existe e não está vazio
            audio_url = query_params.get('audio_url', [''])[0]
            
            if not audio_url:
                self.send_error_response("audio_url parameter is required and cannot be empty")
                return
            
            # Baixa o arquivo de áudio
            audio_file_path = self.download_audio(audio_url)
            if not audio_file_path:
                self.send_error_response("Failed to download audio file")
                return
            
            # Transcreve o áudio
            transcription = self.transcribe_audio(audio_file_path)
            
            # Remove o arquivo temporário
            os.unlink(audio_file_path)
            
            # Retorna a resposta JSON
            response_data = {"response": transcription}
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_error_response(f"Internal server error: {str(e)}")
    
    def download_audio(self, url):
        try:
            # Cria arquivo temporário
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
            temp_file.close()
            
            # Baixa o arquivo
            urllib.request.urlretrieve(url, temp_file.name)
            return temp_file.name
        except Exception as e:
            print(f"Error downloading audio: {e}")
            return None
    
    def transcribe_audio(self, file_path):
        try:
            result = whisper_model.transcribe(file_path)
            return result["text"]
        except Exception as e:
            print(f"Error transcribing audio: {e}")
            return ""
    
    def send_json_response(self, data):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())
    
    def send_error_response(self, message):
        self.send_response(400)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        error_data = {"error": message}
        self.wfile.write(json.dumps(error_data).encode())

def run_server(port=8000):
    server_address = ('', port)
    httpd = HTTPServer(server_address, AudioTranscriptionHandler)
    print(f"Server running on port {port}")
    httpd.serve_forever()

if __name__ == "__main__":
    run_server()
